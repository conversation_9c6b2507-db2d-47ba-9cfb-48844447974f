# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

import PyInstaller

a = Analysis(
    ['pdf_security_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['flet','PyPDF2','hashlib','secrets','string','random','os','sys','datetime','threading'],
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='HielPDFProtector',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # windowed app
    icon=None
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='HielPDFProtector'
) 
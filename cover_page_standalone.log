This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.7.7)  20 JUL 2025 22:20
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**cover_page_standalone.tex
(./cover_page_standalone.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks16
\inpenc@posthook=\toks17
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2022/01/26 3.70 The Babel package
\babel@savecnt=\count193
\U@D=\dimen139
\l@unhyphenated=\language5

(/usr/share/texlive/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count194

(/usr/share/texlive/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'british' set to \l@english
(babel)             (\language0). Reported on input line 82.
Package babel Info: Hyphen rules for 'UKenglish' set to \l@english
(babel)             (\language0). Reported on input line 83.
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@english
(babel)             (\language0). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@english
(babel)             (\language0). Reported on input line 108.
)
(/usr/share/texlive/texmf-dist/tex/generic/babel-french/french.ldf
Language: french 2021/07/20 v3.5m French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language4). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language4). Reported on input line 92.
\FB@nonchar=\count195
Package babel Info: Making : an active character on input line 430.
Package babel Info: Making ; an active character on input line 431.
Package babel Info: Making ! an active character on input line 432.
Package babel Info: Making ? an active character on input line 433.
\FBguill@level=\count196
\FBold@everypar=\toks18
\FB@Mht=\dimen140
\mc@charclass=\count197
\mc@charfam=\count198
\mc@charslot=\count199
\std@mcc=\count266
\dec@mcc=\count267
\listindentFB=\dimen141
\descindentFB=\dimen142
\labelindentFB=\dimen143
\labelwidthFB=\dimen144
\leftmarginFB=\dimen145
\parindentFFN=\dimen146
\FBfnindent=\dimen147
))
(/usr/share/texlive/texmf-dist/tex/latex/carlisle/scalefnt.sty)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen148
\Gin@req@width=\dimen149
)
(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen150
\Gm@wd@mp=\dimen151
\Gm@odd@mp=\dimen152
\Gm@even@mp=\dimen153
\Gm@layoutwidth=\dimen154
\Gm@layoutheight=\dimen155
\Gm@layouthoffset=\dimen156
\Gm@layoutvoffset=\dimen157
\Gm@dimlist=\toks20
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks21
\pgfutil@tempdima=\dimen158
\pgfutil@tempdimb=\dimen159

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box50
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
))
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks22
\pgfkeys@temptoks=\toks23

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks24
))
\pgf@x=\dimen160
\pgf@y=\dimen161
\pgf@xa=\dimen162
\pgf@ya=\dimen163
\pgf@xb=\dimen164
\pgf@yb=\dimen165
\pgf@xc=\dimen166
\pgf@yc=\dimen167
\pgf@xd=\dimen168
\pgf@yd=\dimen169
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count271
\c@pgf@countb=\count272
\c@pgf@countc=\count273
\c@pgf@countd=\count274
\t@pgf@toka=\toks25
\t@pgf@tokb=\toks26
\t@pgf@tokc=\toks27
\pgf@sys@id@count=\count275
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count276
\pgfsyssoftpath@bigbuffer@items=\count277
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen170
\pgfmath@count=\count278
\pgfmath@box=\box51
\pgfmath@toks=\toks28
\pgfmath@stack@operand=\toks29
\pgfmath@stack@operation=\toks30
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count279
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen171
\pgf@picmaxx=\dimen172
\pgf@picminy=\dimen173
\pgf@picmaxy=\dimen174
\pgf@pathminx=\dimen175
\pgf@pathmaxx=\dimen176
\pgf@pathminy=\dimen177
\pgf@pathmaxy=\dimen178
\pgf@xx=\dimen179
\pgf@xy=\dimen180
\pgf@yx=\dimen181
\pgf@yy=\dimen182
\pgf@zx=\dimen183
\pgf@zy=\dimen184
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen185
\pgf@path@lasty=\dimen186
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen187
\pgf@shorten@start@additional=\dimen188
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box52
\pgf@hbox=\box53
\pgf@layerbox@main=\box54
\pgf@picture@serial@count=\count280
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen189
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen190
\pgf@pt@y=\dimen191
\pgf@pt@temp=\dimen192
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen193
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen194
\pgf@sys@shading@range@num=\count281
\pgf@shadingcount=\count282
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box55
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box56
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen195
\pgf@nodesepend=\dimen196
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen197
\pgffor@skip=\dimen198
\pgffor@stack=\toks31
\pgffor@toks=\toks32
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count283
\pgfplotmarksize=\dimen199
)
\tikz@lastx=\dimen256
\tikz@lasty=\dimen257
\tikz@lastxsaved=\dimen258
\tikz@lastysaved=\dimen259
\tikz@lastmovetox=\dimen260
\tikz@lastmovetoy=\dimen261
\tikzleveldistance=\dimen262
\tikzsiblingdistance=\dimen263
\tikz@figbox=\box57
\tikz@figbox@bg=\box58
\tikz@tempbox=\box59
\tikz@tempbox@bg=\box60
\tikztreelevel=\count284
\tikznumberofchildren=\count285
\tikznumberofcurrentchild=\count286
\tikz@fig@count=\count287

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count288
\pgfmatrixcurrentcolumn=\count289
\pgf@matrix@numberofcolumns=\count290
)
\tikz@expandcount=\count291

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.callouts.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.callouts.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodepartlowerbox=\box61
\pgfnodeparttwobox=\box62
\pgfnodepartthreebox=\box63
\pgfnodepartfourbox=\box64
\pgfnodeparttwentybox=\box65
\pgfnodepartnineteenbox=\box66
\pgfnodeparteighteenbox=\box67
\pgfnodepartseventeenbox=\box68
\pgfnodepartsixteenbox=\box69
\pgfnodepartfifteenbox=\box70
\pgfnodepartfourteenbox=\box71
\pgfnodepartthirteenbox=\box72
\pgfnodeparttwelvebox=\box73
\pgfnodepartelevenbox=\box74
\pgfnodeparttenbox=\box75
\pgfnodepartninebox=\box76
\pgfnodeparteightbox=\box77
\pgfnodepartsevenbox=\box78
\pgfnodepartsixbox=\box79
\pgfnodepartfivebox=\box80
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.
tex
File: pgflibraryarrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\arrowsize=\dimen264
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryfit.code.tex
File: tikzlibraryfit.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@layerbox@background=\box81
\pgf@layerboxsaved@background=\box82
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypatterns.code.tex
File: tikzlibrarypatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibrarypatterns.cod
e.tex
File: pgflibrarypatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.pathreplacing.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.cod
e.tex
\pgfdecoratedcompleteddistance=\dimen265
\pgfdecoratedremainingdistance=\dimen266
\pgfdecoratedinputsegmentcompleteddistance=\dimen267
\pgfdecoratedinputsegmentremainingdistance=\dimen268
\pgf@decorate@distancetomove=\dimen269
\pgf@decorate@repeatstate=\count292
\pgfdecorationsegmentamplitude=\dimen270
\pgfdecorationsegmentlength=\dimen271
)
\tikz@lib@dec@box=\box83
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.pathreplacing.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshadows.code.tex
File: tikzlibraryshadows.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryfadings.code.tex
File: tikzlibraryfadings.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryfadings.code
.tex
File: pgflibraryfadings.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/share/texlive/texmf-dist/tex/latex/newtx/newtxtext.sty
Package: newtxtext 2022/01/11 v1.705(Michael Sharpe) latex and unicode latex su
pport for TeXGyreTermesX

`newtxtext' v1.705, 2022/01/11 Text macros taking advantage of TeXGyre Termes a
nd its extensions (msharpe)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
)
(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
(/usr/share/texlive/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2020/11/20 v2.8 package option processing (HA)

(/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/share/texlive/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks33
\XKV@tempa@toks=\toks34
)
\XKV@depth=\count293
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count294
)
(/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.sty
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.tex
\integerpart=\count295
\decimalpart=\count296
)
Package: xstring 2021/07/21 v1.84 String manipulations (CT)
)
(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
)
LaTeX Font Info:    Setting ntxLF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxTLF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxOsF sub-encoding to TS1/0 on input line 27.
LaTeX Font Info:    Setting ntxTOsF sub-encoding to TS1/0 on input line 27.

(/usr/share/texlive/texmf-dist/tex/generic/kastrup/binhex.tex)
\ntx@tmpcnta=\count297
\ntx@cnt=\count298

(/usr/share/texlive/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
LaTeX Info: Redefining \textsu on input line 541.
LaTeX Info: Redefining \oldstylenums on input line 631.
)
(/usr/share/texlive/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2021/12/18 v1.7

`newtxmath' v1.7, 2021/12/18 Math macros based originally on txfonts (msharpe)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip49

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen272
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen273
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count299
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count300
\leftroot@=\count301
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count302
\DOTSCASE@=\count303
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box84
\strutbox@=\box85
\big@size=\dimen274
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count304
\c@MaxMatrixCols=\count305
\dotsspace@=\muskip16
\c@parentequation=\count306
\dspbrk@lvl=\count307
\tag@help=\toks36
\row@=\count308
\column@=\count309
\maxfields@=\count310
\andhelp@=\toks37
\eqnshift@=\dimen275
\alignsep@=\dimen276
\tagshift@=\dimen277
\tagwidth@=\dimen278
\totwidth@=\dimen279
\lineht@=\dimen280
\@envbody=\toks38
\multlinegap=\skip50
\multlinetaggap=\skip51
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
)
(/usr/share/texlive/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count311

(/usr/share/texlive/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count312
\tx@IsAlNum=\count313
\tx@tA=\toks40
\tx@tB=\toks41
\tx@su=\read4

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/ntxtlf/m/n on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/m/n on input line 373.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/ntxtlf/m/n --> OT1/ntxtlf/b/n on input line 374.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 380.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/qhv/m/n on input line 380.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> T1/qhv/m/n on input line 380.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 381.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/ntxtlf/m/it on input line 381.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/ntxtlf/m/it on input line 381.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 382.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> T1/ntxtt/m/n on input line 382.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> T1/ntxtt/m/n on input line 382.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/b/n on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/ntxtlf/b/n on input line 384.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/ntxtlf/m/it --> OT1/ntxtlf/b/it on input line 385.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/qhv/m/n --> T1/qhv/b/n on input line 386.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/ntxtt/m/n --> T1/ntxtt/b/n on input line 387.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 488.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 489.
\symlettersA=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 544.
Now handling font encoding LMS ...
... no UTF-8 mapping file for font encoding LMS
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 565.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 565.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 565.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 566.
\symAMSm=\mathgroup5
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 591.
\symsymbolsC=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 612.
Now handling font encoding LMX ...
... no UTF-8 mapping file for font encoding LMX
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 625.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 6
25.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 625.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 625
.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 625.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 626.
\symlargesymbolsTXA=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'

(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 640.
\tx@sbptoks=\toks42
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 863.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 864.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 865.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 866.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 867.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 868.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 869.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 871.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 875.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 876.
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 922.
LaTeX Font Info:    Redeclaring math accent \dot on input line 947.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 948.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2017.
LaTeX Info: Redefining \Bbbk on input line 2807.
LaTeX Info: Redefining \not on input line 2956.
)
LaTeX Font Info:    Trying to load font information for T1+ntxtlf on input line
 29.
 (/usr/share/texlive/texmf-dist/tex/latex/newtx/t1ntxtlf.fd
File: t1ntxtlf.fd 2021/05/24 v1.1 font definition file for T1/ntx/tlf
)
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 29.

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count314
\l__pdf_internal_box=\box86
)
No file cover_page_standalone.aux.
\openout1 = `cover_page_standalone.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 29.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line
 29.
(/usr/share/texlive/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 29.
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 29.
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 29.
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 29.
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 29.
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 29.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 
29.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 29.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line
 29.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 29.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 29.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input lin
e 29.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 29.
LaTeX Info: Redefining \degres on input line 29.
LaTeX Info: Redefining \up on input line 29.

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count315
\scratchdimen=\dimen281
\scratchbox=\box87
\nofMPsegments=\count316
\nofMParguments=\count317
\everyMPshowfont=\toks43
\MPscratchCnt=\count318
\MPscratchDim=\dimen282
\MPnumerator=\count319
\makeMPintoPDFobject=\count320
\everyMPtoPDFconversion=\toks44
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(0.0pt, 597.50787pt, 0.0pt)
* v-part:(T,H,B)=(0.0pt, 845.04684pt, 0.0pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=597.50787pt
* \textheight=845.04684pt
* \oddsidemargin=-72.26999pt
* \evensidemargin=-72.26999pt
* \topmargin=-109.26999pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\c@mv@tabular=\count321
\c@mv@boldtabular=\count322
*geometry* verbose mode - [ newgeometry ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(42.67912pt, 512.14963pt, 42.67912pt)
* v-part:(T,H,B)=(28.45274pt, 773.91498pt, 42.67912pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=512.14963pt
* \textheight=773.91498pt
* \oddsidemargin=-29.59087pt
* \evensidemargin=-29.59087pt
* \topmargin=-80.81725pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

<preliminaries/logo_university.png, id=29, 642.4pt x 368.37625pt>
File: preliminaries/logo_university.png Graphic file (type png)
<use preliminaries/logo_university.png>
Package pdftex.def Info: preliminaries/logo_university.png  used on input line 
41.
(pdftex.def)             Requested size: 119.84196pt x 68.72186pt.
LaTeX Font Info:    Trying to load font information for OT1+ntxtlf on input lin
e 42.
(/usr/share/texlive/texmf-dist/tex/latex/newtx/ot1ntxtlf.fd
File: ot1ntxtlf.fd 2021/05/24 v1.0 font definition file for OT1/ntx/tlf
)
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 12.0pt on input line 42.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 8.8pt on input line 42.
LaTeX Font Info:    Font shape `OT1/ntxtlf/m/n' will be
(Font)              scaled to size 6.6pt on input line 42.
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 
42.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2018/04/14 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 
42.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2015/03/20 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 
42.

(/usr/share/texlive/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)
<preliminaries/logo_agevolami.png, id=31, 376.40625pt x 376.40625pt>
File: preliminaries/logo_agevolami.png Graphic file (type png)
<use preliminaries/logo_agevolami.png>
Package pdftex.def Info: preliminaries/logo_agevolami.png  used on input line 4
5.
(pdftex.def)             Requested size: 73.97614pt x 73.97614pt.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 14.4pt on input line 49.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 14.4pt on input line 49.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 12.0pt on input line 51.
LaTeX Font Info:    Font shape `T1/ntxtlf/m/n' will be
(Font)              scaled to size 17.28pt on input line 58.
LaTeX Font Info:    Font shape `T1/ntxtlf/b/n' will be
(Font)              scaled to size 17.28pt on input line 60.

Overfull \hbox (6.39093pt too wide) in paragraph at lines 62--63
 [] 
 []

LaTeX Font Info:    Font shape `T1/ntxtlf/m/it' will be
(Font)              scaled to size 12.0pt on input line 66.
[1


{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map} <./preliminaries/logo_unive
rsity.png> <./preliminaries/logo_agevolami.png>] (./cover_page_standalone.aux) 
) 
Here is how much of TeX's memory you used:
 22073 strings out of 480218
 443640 string characters out of 5894883
 724934 words of memory out of 5000000
 39620 multiletter control sequences out of 15000+600000
 501829 words of font info for 61 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 84i,9n,83p,508b,863s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/newtx/ntx-ec-tlf.enc}</usr/shar
e/texlive/texmf-dist/fonts/type1/public/newtx/ztmb.pfb></usr/share/texlive/texm
f-dist/fonts/type1/public/newtx/ztmr.pfb></usr/share/texlive/texmf-dist/fonts/t
ype1/public/newtx/ztmri.pfb>
Output written on cover_page_standalone.pdf (1 page, 266956 bytes).
PDF statistics:
 56 PDF objects out of 1000 (max. 8388607)
 26 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 119 words of extra memory for PDF output out of 10000 (max. 10000000)


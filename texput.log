This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.7.7)  12 JUL 2025 16:20
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**main.py

! Emergency stop.
<*> main.py
           
End of file on the terminal!

 
Here is how much of TeX's memory you used:
 4 strings out of 480218
 118 string characters out of 5894883
 284057 words of memory out of 5000000
 18058 multiletter control sequences out of 15000+600000
 469259 words of font info for 28 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 0i,0n,0p,1b,6s stack positions out of 5000i,500n,10000p,200000b,80000s
!  ==> Fatal error occurred, no output PDF file produced!
